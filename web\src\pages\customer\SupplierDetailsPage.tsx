import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Star, Clock, Phone, Search, Plus, Minus, ShoppingCart, Heart, Share2, Sparkles, Grid3X3, List, LayoutGrid, Store, Package, ImageIcon } from 'lucide-react';
import { motion } from 'framer-motion';
import { useCartStore } from '../../stores/cartStore';
import { apiService } from '../../services/api';
import type { Supplier, Product } from '../../services/api';

// Utility functions for safe field access and backward compatibility
const safeGet = <T,>(obj: any, path: string, defaultValue: T): T => {
  try {
    return path.split('.').reduce((current, key) => current?.[key], obj) ?? defaultValue;
  } catch {
    return defaultValue;
  }
};

const getImageWithFallback = (src: string | undefined, fallback: string) => {
  return src && src.trim() !== '' ? src : fallback;
};

const formatPrice = (price: number | undefined | null): string => {
  if (typeof price === 'number' && !isNaN(price)) {
    return price.toFixed(2);
  }
  return '0.00';
};

// Beautiful fallback components for missing images
const SupplierLogoFallback = ({ name, className }: { name: string; className: string }) => (
  <div className={`${className} bg-gradient-to-br from-purple-500 via-blue-500 to-indigo-600 flex items-center justify-center relative overflow-hidden`}>
    <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
    <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.3),transparent_50%)]"></div>
    <Store className="w-1/2 h-1/2 text-white/90 relative z-10" />
    <div className="absolute bottom-1 right-1 text-xs font-bold text-white/70 bg-black/20 px-1 rounded">
      {name.charAt(0).toUpperCase()}
    </div>
  </div>
);

const SupplierBannerFallback = ({ name, category, className }: { name: string; category: string; className: string }) => (
  <div className={`${className} bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 flex items-center justify-center relative overflow-hidden`}>
    {/* Animated background pattern */}
    <div className="absolute inset-0 opacity-20">
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(255,255,255,0.3),transparent_50%)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(255,255,255,0.2),transparent_50%)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_40%_40%,rgba(255,255,255,0.1),transparent_50%)]"></div>
    </div>

    {/* Floating particles */}
    <motion.div
      animate={{
        y: [-10, 10, -10],
        rotate: [0, 5, -5, 0]
      }}
      transition={{
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut"
      }}
      className="absolute top-4 left-4 w-3 h-3 bg-white/30 rounded-full"
    />
    <motion.div
      animate={{
        y: [10, -10, 10],
        rotate: [0, -5, 5, 0]
      }}
      transition={{
        duration: 8,
        repeat: Infinity,
        ease: "easeInOut",
        delay: 1
      }}
      className="absolute top-8 right-8 w-2 h-2 bg-white/20 rounded-full"
    />
    <motion.div
      animate={{
        y: [-5, 15, -5],
        rotate: [0, 10, -10, 0]
      }}
      transition={{
        duration: 7,
        repeat: Infinity,
        ease: "easeInOut",
        delay: 2
      }}
      className="absolute bottom-6 left-1/3 w-4 h-4 bg-white/25 rounded-full"
    />

    {/* Main content */}
    <div className="text-center relative z-10">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="mb-4"
      >
        <Store className="w-16 h-16 text-white/90 mx-auto mb-2" />
      </motion.div>
      <motion.h3
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="text-2xl font-bold text-white mb-1"
      >
        {name}
      </motion.h3>
      <motion.p
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="text-white/80 capitalize"
      >
        {category}
      </motion.p>
    </div>
  </div>
);

const ProductImageFallback = ({ name, category, className }: { name: string; category: string; className: string }) => {
  const getCategoryIcon = (cat: string) => {
    const lowerCat = cat.toLowerCase();
    if (lowerCat.includes('food') || lowerCat.includes('restaurant')) return '🍽️';
    if (lowerCat.includes('clothing') || lowerCat.includes('fashion')) return '👕';
    if (lowerCat.includes('electronics')) return '📱';
    if (lowerCat.includes('book')) return '📚';
    if (lowerCat.includes('health') || lowerCat.includes('pharmacy')) return '💊';
    return '📦';
  };

  return (
    <div className={`${className} bg-gradient-to-br from-gray-100 via-gray-50 to-white flex items-center justify-center relative overflow-hidden border-2 border-gray-200/50`}>
      {/* Subtle background pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,rgba(139,69,19,0.1),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_70%,rgba(75,85,99,0.1),transparent_50%)]"></div>
      </div>

      {/* Animated elements */}
      <motion.div
        animate={{
          scale: [1, 1.1, 1],
          rotate: [0, 5, -5, 0]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="absolute top-2 right-2 text-2xl opacity-20"
      >
        {getCategoryIcon(category)}
      </motion.div>

      {/* Main content */}
      <div className="text-center relative z-10 p-4">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="mb-2"
        >
          <Package className="w-8 h-8 text-gray-400 mx-auto" />
        </motion.div>
        <motion.p
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="text-xs text-gray-500 font-medium line-clamp-2"
        >
          {name}
        </motion.p>
      </div>

      {/* Corner decoration */}
      <div className="absolute bottom-0 right-0 w-6 h-6 bg-gradient-to-tl from-gray-300/30 to-transparent"></div>
    </div>
  );
};

// Enhanced image component with beautiful fallbacks
const EnhancedImage = ({
  src,
  alt,
  className,
  fallbackType = 'product',
  name = '',
  category = '',
  onError,
  ...props
}: {
  src: string;
  alt: string;
  className: string;
  fallbackType?: 'supplier-logo' | 'supplier-banner' | 'product';
  name?: string;
  category?: string;
  onError?: () => void;
  [key: string]: any;
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
    onError?.();
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  if (imageError || !src || src.trim() === '') {
    switch (fallbackType) {
      case 'supplier-logo':
        return <SupplierLogoFallback name={name} className={className} />;
      case 'supplier-banner':
        return <SupplierBannerFallback name={name} category={category} className={className} />;
      case 'product':
        return <ProductImageFallback name={name} category={category} className={className} />;
      default:
        return <ProductImageFallback name={name} category={category} className={className} />;
    }
  }

  return (
    <div className="relative">
      {isLoading && (
        <div className={`${className} bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center absolute inset-0 z-10`}>
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-6 h-6 border-2 border-purple-500 border-t-transparent rounded-full"
          />
        </div>
      )}
      <img
        src={src}
        alt={alt}
        className={className}
        onError={handleImageError}
        onLoad={handleImageLoad}
        {...props}
      />
    </div>
  );
};

// Enhanced compatibility layer for supplier data
const normalizeSupplier = (supplier: any): Supplier => {
  return {
    _id: supplier._id || supplier.id || '',
    id: supplier.id || supplier._id || '',
    name: supplier.name || 'Unknown Supplier',
    lat: typeof supplier.lat === 'number' ? supplier.lat : 0,
    lng: typeof supplier.lng === 'number' ? supplier.lng : 0,
    category: supplier.category || 'general',
    rating: typeof supplier.rating === 'number' ? Math.max(0, Math.min(5, supplier.rating)) : 0,
    tags: Array.isArray(supplier.tags) ? supplier.tags : [],
    logoUrl: supplier.logoUrl || '/default-supplier-logo.png',
    banner: supplier.banner || '/default-supplier-banner.jpg',
    openHours: supplier.openHours || 'Hours not available',
    deliveryTime: supplier.deliveryTime || 'Time not available',
    phone: supplier.phone || 'Phone not available',
    address: supplier.address || undefined,
    description: supplier.description || undefined,
    isActive: supplier.isActive !== false, // Default to true unless explicitly false
    products: Array.isArray(supplier.products) ? supplier.products.map(normalizeProduct) : [],
    createdAt: supplier.createdAt || new Date().toISOString(),
    updatedAt: supplier.updatedAt || new Date().toISOString(),
  };
};

// Enhanced compatibility layer for product data
const normalizeProduct = (product: any): Product => {
  return {
    id: product.id || product._id || '',
    name: product.name || 'Unnamed Product',
    image: product.image || '/default-product-image.jpg',
    price: typeof product.price === 'number' ? Math.max(0, product.price) : 0,
    discountPrice: typeof product.discountPrice === 'number' ? Math.max(0, product.discountPrice) : undefined,
    category: product.category || 'general',
    description: product.description || undefined,
    isAvailable: product.isAvailable !== false, // Default to true unless explicitly false
    tags: Array.isArray(product.tags) ? product.tags : undefined,
    nutritionInfo: product.nutritionInfo || undefined,
    allergens: Array.isArray(product.allergens) ? product.allergens : undefined,
    preparationTime: product.preparationTime || undefined,
    customizations: Array.isArray(product.customizations) ? product.customizations : undefined,
    restaurantOptions: product.restaurantOptions ? {
      additions: Array.isArray(product.restaurantOptions.additions) ? product.restaurantOptions.additions : undefined,
      without: Array.isArray(product.restaurantOptions.without) ? product.restaurantOptions.without : undefined,
      sides: Array.isArray(product.restaurantOptions.sides) ? product.restaurantOptions.sides : undefined,
    } : undefined,
    clothingOptions: product.clothingOptions ? {
      sizes: Array.isArray(product.clothingOptions.sizes) ? product.clothingOptions.sizes : [],
      colors: Array.isArray(product.clothingOptions.colors) ? product.clothingOptions.colors : [],
      gallery: Array.isArray(product.clothingOptions.gallery) ? product.clothingOptions.gallery : [],
    } : undefined,
  };
};

const SupplierDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const supplierId = searchParams.get('supplierId');

  // State for API data
  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [productCategories, setProductCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [productsLoading, setProductsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // UI state
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const { addItem, items, totalQty } = useCartStore();
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [layoutMode, setLayoutMode] = useState<'list' | 'grid' | 'compact'>('list');

  // Load supplier data (only once when component mounts)
  useEffect(() => {
    const loadSupplierData = async () => {
      if (!supplierId) {
        setError('Supplier ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Only fetch supplier data initially
        const supplierResponse = await apiService.getSupplierById(supplierId);

        if (supplierResponse.success && supplierResponse.data) {
          const normalizedSupplier = normalizeSupplier(supplierResponse.data);
          setSupplier(normalizedSupplier);
        } else {
          throw new Error(supplierResponse.message || 'Failed to load supplier');
        }
      } catch (err) {
        console.error('Error loading supplier data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load supplier data');
      } finally {
        setLoading(false);
      }
    };

    loadSupplierData();
  }, [supplierId]);

  // Separate effect for loading products with debounced search
  useEffect(() => {
    const loadProducts = async () => {
      if (!supplierId) return;

      try {
        setProductsLoading(true);
        const productsResponse = await apiService.getSupplierProducts(supplierId, {
          limit: 100,
          category: selectedCategory !== 'All' ? selectedCategory : undefined,
          search: searchQuery.trim() || undefined
        });

        if (productsResponse.success && productsResponse.data) {
          const normalizedProducts = (productsResponse.data.products || []).map(normalizeProduct);
          setProducts(normalizedProducts);
          setProductCategories(productsResponse.data.categories || []);
        } else {
          console.error('Failed to load products:', productsResponse.message);
          setProducts([]);
        }
      } catch (err) {
        console.error('Error loading products:', err);
        setProducts([]);
      } finally {
        setProductsLoading(false);
      }
    };

    // Debounce search queries to avoid excessive API calls
    const timeoutId = setTimeout(() => {
      loadProducts();
    }, searchQuery.trim() ? 300 : 0); // 300ms delay for search, immediate for category changes

    return () => clearTimeout(timeoutId);
  }, [supplierId, selectedCategory, searchQuery]);

  // Handle scroll for header animation with debouncing (matching home page pattern)
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      // Clear previous timeout
      clearTimeout(timeoutId);

      // Debounce the header compact state change
      timeoutId = setTimeout(() => {
        // Much higher threshold to prevent premature hiding
        setIsHeaderCompact(currentScrollY > 800);
      }, 100);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Get unique categories from products (combine backend categories with product categories)
  const categories = useMemo(() => {
    const productCats = Array.from(new Set(products.map(p => p.category)));
    const allCats = [...new Set([...productCategories, ...productCats])];
    // Filter out "All" from the categories to avoid duplicates, then add it at the beginning
    const filteredCats = allCats.filter(cat => cat !== 'All');
    return ['All', ...filteredCats];
  }, [products, productCategories]);

  // Since we're doing server-side filtering, just filter for available products
  const filteredProducts = useMemo(() => {
    return products.filter(product => product.isAvailable !== false);
  }, [products]);

  const addToCart = (product: Product) => {
    if (!supplier) return;
    addItem({
      product: {
        id: product.id,
        name: product.name,
        image: product.image,
        price: product.price,
        category: product.category,
        restaurantOptions: product.restaurantOptions,
        clothingOptions: product.clothingOptions
      },
      qty: 1,
      finalPrice: product.discountPrice || product.price,
      supplierId: supplier.id,
      supplierName: supplier.name,
      supplierCategory: supplier.category
    });
  };

  const getCartQty = (productId: string) => {
    const item = items.find(i =>
      i.product.id === productId &&
      i.supplierId === supplier?.id
    );
    return item ? item.qty : 0;
  };

  const handleProductClick = (product: Product) => {
    navigate(`/customer/supplier-product-details?productId=${product.id}&supplierId=${supplierId}&category=${supplier?.category}`, {
      state: { product, supplier }
    });
  };

  const handleCheckout = () => {
    if (!supplier) return;
    const supplierItems = items.filter(i => i.supplierId === supplier.id);
    if (supplierItems.length > 0) {
      navigate('/customer/order-checkout', {
        state: {
          itemsBySupplier: { [supplier.id]: supplierItems },
          totalWithoutFee: totalQty(supplier.id)
        }
      });
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Supplier...</h2>
          <p className="text-gray-600">Please wait while we fetch the supplier details</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !supplier) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {error || 'Supplier Not Found'}
          </h2>
          <p className="text-gray-600 mb-6">
            {error || 'The supplier you are looking for could not be found.'}
          </p>
          <button
            onClick={() => navigate(-1)}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }



  return (
    <>
      {/* Sticky Header with Scroll Animation*/}
      <motion.div
        className="fixed top-0 left-0 right-0 z-50 transition-all duration-500"
        animate={{
          backgroundColor: isHeaderCompact
            ? "rgba(15, 23, 42, 0.95)"
            : "rgba(15, 23, 42, 0)",
          backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
          borderBottom: isHeaderCompact
            ? "1px solid rgba(255, 255, 255, 0.1)"
            : "1px solid rgba(255, 255, 255, 0)",
        }}
        transition={{ duration: 0.3 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            animate={{
              paddingTop: isHeaderCompact ? "1rem" : "2rem",
              paddingBottom: isHeaderCompact ? "1rem" : "2rem",
            }}
            transition={{ duration: 0.3 }}
          >
            {/* Compact Header Content */}
            <motion.div
              animate={{
                opacity: isHeaderCompact ? 1 : 0,
                height: isHeaderCompact ? "auto" : 0,
              }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => navigate(-1)}
                    className="p-2 bg-white/10 hover:bg-white/20 rounded-xl transition-all duration-200"
                  >
                    <ArrowLeft className="text-white" size={20} />
                  </motion.button>
                  <div className="flex items-center gap-3">
                    <EnhancedImage
                      src={supplier.logoUrl}
                      alt={supplier.name || 'Supplier'}
                      className="w-8 h-8 rounded-lg border border-white/30"
                      fallbackType="supplier-logo"
                      name={supplier.name}
                      category={supplier.category}
                    />
                    <div>
                      <h1 className="text-xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
                        {supplier.name || 'Unknown Supplier'}
                      </h1>
                      <p className="text-white/60 text-xs">Supplier Details</p>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  {/* Mini Search */}
                  <div className="hidden md:flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 px-3 py-2">
                    <Search size={16} className="text-white/60" />
                    <input
                      type="text"
                      placeholder="Search products..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="bg-transparent text-white placeholder-white/50 outline-none text-sm w-40"
                    />
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </motion.div>

      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
                  {/* Animated gradient orbs */}
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.3, 0.6, 0.3],
                    }}
                    transition={{
                      duration: 8,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-orange-500/30 to-red-600/30 rounded-full blur-3xl"
                  />
                  <motion.div
                    animate={{
                      scale: [1.2, 1, 1.2],
                      opacity: [0.4, 0.7, 0.4],
                    }}
                    transition={{
                      duration: 10,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 2
                    }}
                    className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-pink-500/30 to-purple-600/30 rounded-full blur-3xl"
                  />
                  <motion.div
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.2, 0.5, 0.2],
                    }}
                    transition={{
                      duration: 12,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 4
                    }}
                    className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-blue-500/30 to-cyan-600/30 rounded-full blur-3xl"
                  />
        
                  {/* Floating particles */}
                  {[...Array(15)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-2 h-2 bg-white/20 rounded-full"
                      style={{
                        left: `${Math.random() * 100}%`,
                        top: `${Math.random() * 100}%`,
                      }}
                      animate={{
                        y: [-20, -100, -20],
                        opacity: [0, 1, 0],
                      }}
                      transition={{
                        duration: 3 + Math.random() * 2,
                        repeat: Infinity,
                        delay: Math.random() * 2,
                      }}
                    />
                  ))}
        </div>

        {/* Premium Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="relative z-10 bg-white/10 backdrop-blur-xl border-b border-white/20"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center gap-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate(-1)}
                className="p-3 bg-white/20 backdrop-blur-sm hover:bg-white/30 rounded-xl transition-all duration-300 border border-white/20"
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </motion.button>
              <motion.h1
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
                className="text-3xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent"
              >
                {supplier.name || 'Unknown Supplier'}
              </motion.h1>
            </div>
          </div>
        </motion.div>

        {/* Main Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Premium Supplier Info Card */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 overflow-hidden mb-8 shadow-2xl"
          >
            <div className="relative">
              <motion.div
                initial={{ scale: 1.1 }}
                animate={{ scale: 1 }}
                transition={{ duration: 1.2 }}
                className="w-full h-64"
              >
                <EnhancedImage
                  src={supplier.banner}
                  alt={supplier.name || 'Supplier'}
                  className="w-full h-64 object-cover"
                  fallbackType="supplier-banner"
                  name={supplier.name}
                  category={supplier.category}
                />
              </motion.div>
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />

              {/* Floating Logo */}
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ delay: 0.6, type: "spring", stiffness: 200 }}
                className="absolute bottom-6 left-6"
              >
                <div className="relative">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl opacity-75 blur-lg"
                  />
                  <EnhancedImage
                    src={supplier.logoUrl}
                    alt={supplier.name || 'Supplier'}
                    className="relative w-20 h-20 rounded-2xl border-4 border-white/50 shadow-2xl backdrop-blur-sm"
                    fallbackType="supplier-logo"
                    name={supplier.name}
                    category={supplier.category}
                  />
                </div>
              </motion.div>

              {/* Action Buttons */}
              <div className="absolute top-6 right-6 flex gap-3">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="p-3 bg-white/20 backdrop-blur-sm rounded-xl border border-white/30 hover:bg-white/30 transition-all duration-300"
                >
                  <Heart className="w-5 h-5 text-white" />
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="p-3 bg-white/20 backdrop-blur-sm rounded-xl border border-white/30 hover:bg-white/30 transition-all duration-300"
                >
                  <Share2 className="w-5 h-5 text-white" />
                </motion.button>
              </div>
            </div>

            <div className="p-8">
              <div className="flex justify-between items-start mb-6">
                <div className="flex-1">
                  <motion.h2
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                    className="text-4xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent mb-2"
                  >
                    {supplier.name || 'Unknown Supplier'}
                  </motion.h2>
                  {supplier.description && (
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5 }}
                      className="text-white/70 text-lg"
                    >
                      {supplier.description}
                    </motion.p>
                  )}
                </div>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.6, type: "spring" }}
                  className={`px-4 py-2 rounded-full text-sm font-semibold ${
                    supplier.isActive !== false
                      ? 'bg-gradient-to-r from-green-400 to-emerald-500 text-white shadow-lg shadow-green-500/25'
                      : 'bg-gradient-to-r from-red-400 to-pink-500 text-white shadow-lg shadow-red-500/25'
                  }`}
                >
                  {supplier.isActive !== false ? '🟢 Open Now' : '🔴 Closed'}
                </motion.div>
              </div>

              {/* Enhanced Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                  className="flex items-center gap-3 p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20"
                >
                  <div className="p-2 bg-yellow-500/20 rounded-lg">
                    <Star className="w-5 h-5 text-yellow-400" />
                  </div>
                  <div>
                    <p className="text-white/60 text-sm">Rating</p>
                    <p className="text-white font-semibold text-lg">{supplier.rating || 0} ⭐</p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}
                  className="flex items-center gap-3 p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20"
                >
                  <div className="p-2 bg-blue-500/20 rounded-lg">
                    <Clock className="w-5 h-5 text-blue-400" />
                  </div>
                  <div>
                    <p className="text-white/60 text-sm">Hours</p>
                    <p className="text-white font-semibold text-lg">{supplier.openHours || 'Hours not available'}</p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.9 }}
                  className="flex items-center gap-3 p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20"
                >
                  <div className="p-2 bg-green-500/20 rounded-lg">
                    <Phone className="w-5 h-5 text-green-400" />
                  </div>
                  <div>
                    <p className="text-white/60 text-sm">Contact</p>
                    <p className="text-white font-semibold text-lg">{supplier.phone || 'Phone not available'}</p>
                  </div>
                </motion.div>
              </div>

              {/* Premium Tags */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1 }}
                className="flex flex-wrap gap-3"
              >
                {supplier.tags?.map((tag, index) => (
                  <motion.span
                    key={tag}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 1 + index * 0.1 }}
                    className="px-4 py-2 bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-sm text-white/90 text-sm rounded-full border border-white/20 hover:border-white/40 transition-all duration-300"
                  >
                    {tag}
                  </motion.span>
                ))}
              </motion.div>
            </div>
          </motion.div>

          {/* Note: Promotions section removed - no promotions API available yet */}

          {/* Premium Search and Categories */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 mb-8 shadow-xl"
          >
            <div className="space-y-6">
              {/* Layout Controls */}
              <div className="flex items-center justify-between">
                <motion.h3
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.8 }}
                  className="text-xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent"
                >
                  Browse Products
                </motion.h3>

                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.9 }}
                  className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-xl p-2 border border-white/20"
                >
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setLayoutMode('list')}
                    className={`p-2 rounded-lg transition-all duration-300 ${
                      layoutMode === 'list'
                        ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg'
                        : 'text-white/60 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <List className="w-4 h-4" />
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setLayoutMode('grid')}
                    className={`p-2 rounded-lg transition-all duration-300 ${
                      layoutMode === 'grid'
                        ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg'
                        : 'text-white/60 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <LayoutGrid className="w-4 h-4" />
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setLayoutMode('compact')}
                    className={`p-2 rounded-lg transition-all duration-300 ${
                      layoutMode === 'compact'
                        ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg'
                        : 'text-white/60 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </motion.button>
                </motion.div>
              </div>
              {/* Enhanced Search */}
              <div className="relative z-10">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.8, type: "spring" }}
                  className="absolute left-4 top-1/4 transform -translate-y-1/2 p-2 bg-purple-500/20 rounded-lg z-20 pointer-events-none"
                >
                  <Search className="text-purple-300 w-5 h-5" />
                </motion.div>
                <motion.input
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.9 }}
                  type="text"
                  placeholder="Search for amazing products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="relative w-full pl-16 pr-6 py-4 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 text-lg z-10"
                />
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full opacity-20 z-20 pointer-events-none"
                />
              </div>

              {/* Ultra Premium Category Tabs with Enhanced Horizontal Scroll */}
              <div className="relative">
                <div className="flex gap-4 overflow-x-auto pb-4 pt-2 px-2 scrollbar-thin scrollbar-track-white/10 scrollbar-thumb-primary-500/50 hover:scrollbar-thumb-primary-500/70 scroll-smooth">
                  {categories.map((category, index) => (
                    <motion.button
                      key={`category-${index}-${category}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 1 + index * 0.1 }}
                      whileHover={{ scale: 1.08, y: -3 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setSelectedCategory(category)}
                      className={`px-8 py-4 rounded-2xl text-sm font-bold whitespace-nowrap transition-all duration-300 border relative overflow-hidden group flex-shrink-0 ${
                        selectedCategory === category
                          ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white border-white/30 shadow-lg shadow-primary-500/25'
                          : 'bg-white/10 backdrop-blur-sm text-white/80 border-white/20 hover:bg-white/20 hover:border-white/40'
                      }`}
                    >
                      {selectedCategory === category && (
                        <motion.div
                          layoutId="activeCategoryTab"
                          className="absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl"
                          transition={{ type: "spring", stiffness: 300, damping: 30 }}
                        />
                      )}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      />
                      <span className="relative z-10 flex items-center gap-2">
                        <motion.span
                          animate={{
                            scale: selectedCategory === category ? [1, 1.2, 1] : 1,
                            rotate: selectedCategory === category ? [0, 360] : 0
                          }}
                          transition={{
                            duration: 2,
                            repeat: selectedCategory === category ? Infinity : 0
                          }}
                        >
                          {category === 'All' ? '🌟' : '📦'}
                        </motion.span>
                        {category}
                        {selectedCategory === category && (
                          <motion.span
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="ml-2 inline-block w-2 h-2 bg-white rounded-full"
                          />
                        )}
                      </span>
                    </motion.button>
                  ))}
                </div>

                {/* Scroll hint indicator */}
                <motion.div
                  initial={{ opacity: 1 }}
                  animate={{ opacity: [1, 0.5, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/40 text-xs pointer-events-none"
                >
                  ← Scroll →
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Dynamic Products Layout */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.9 }}
            className="relative"
          >
            {/* Products Loading Overlay */}
            {productsLoading && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 bg-black/20 backdrop-blur-sm rounded-2xl z-10 flex items-center justify-center"
              >
                <div className="bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20">
                  <div className="flex items-center gap-3 text-white">
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span className="text-sm font-medium">Searching products...</span>
                  </div>
                </div>
              </motion.div>
            )}
            {/* List Layout */}
            {layoutMode === 'list' && (
              <div className="space-y-6">
                {filteredProducts.map((product, index) => (
                  <motion.div
                    key={product.id}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1 + index * 0.1 }}
                    whileHover={{ scale: 1.02, y: -5 }}
                    className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 shadow-xl hover:shadow-2xl transition-all duration-500 group"
                  >
                    <div className="flex gap-6">
                      {/* Premium Product Image */}
                      <motion.div
                        whileHover={{ scale: 1.1, rotate: 2 }}
                        className="relative cursor-pointer"
                        onClick={() => handleProductClick(product)}
                      >
                        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        <EnhancedImage
                          src={product.image}
                          alt={product.name || 'Product'}
                          className="relative w-24 h-24 object-cover rounded-2xl border-2 border-white/30 shadow-lg"
                          fallbackType="product"
                          name={product.name}
                          category={product.category}
                        />
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center"
                        >
                          <Sparkles className="w-3 h-3 text-white" />
                        </motion.div>
                      </motion.div>

                      {/* Product Info */}
                      <div className="flex-1">
                        <motion.h4
                          whileHover={{ x: 5 }}
                          className="font-bold text-xl text-white cursor-pointer hover:text-purple-300 transition-colors duration-300 mb-2"
                          onClick={() => handleProductClick(product)}
                        >
                          {product.name || 'Unnamed Product'}
                        </motion.h4>
                        {product.description && (
                          <p className="text-white/70 text-sm mb-3 leading-relaxed">{product.description}</p>
                        )}
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            {product.discountPrice && product.discountPrice < product.price && (
                              <motion.p
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: 1.2 + index * 0.1 }}
                                className="text-lg font-semibold text-white/50 line-through"
                              >
                                ₪{formatPrice(product.price)}
                              </motion.p>
                            )}
                            <motion.p
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ delay: 1.2 + index * 0.1 }}
                              className="text-2xl font-bold bg-gradient-to-r from-green-400 to-emerald-500 bg-clip-text text-transparent"
                            >
                              ₪{formatPrice(product.discountPrice || product.price)}
                            </motion.p>
                          </div>
                          {product.discountPrice && product.discountPrice < product.price ? (
                            <span className="px-2 py-1 bg-red-500/20 text-red-300 text-xs rounded-full border border-red-500/30">
                              Sale!
                            </span>
                          ) : (
                            <span className="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full border border-green-500/30">
                              Best Price
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Enhanced Cart Controls */}
                      <div className="flex items-center gap-3">
                        {getCartQty(product.id) > 0 ? (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-xl p-2 border border-white/20"
                          >
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => {
                                const item = items.find(i => i.product.id === product.id && i.supplierId === supplier.id);
                                if (item && item.qty > 1) {
                                  useCartStore.getState().updateQty(item.id, item.qty - 1);
                                } else if (item) {
                                  useCartStore.getState().removeItem(item.id);
                                }
                              }}
                              className="w-10 h-10 rounded-xl bg-gradient-to-r from-red-500 to-pink-500 text-white flex items-center justify-center hover:shadow-lg hover:shadow-red-500/25 transition-all duration-300"
                            >
                              <Minus className="w-4 h-4" />
                            </motion.button>
                            <motion.span
                              key={`cart-qty-list-${product.id}`}
                              initial={{ scale: 1.5 }}
                              animate={{ scale: 1 }}
                              className="w-10 text-center font-bold text-white text-lg"
                            >
                              {getCartQty(product.id)}
                            </motion.span>
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => {
                                const item = items.find(i => i.product.id === product.id && i.supplierId === supplier.id);
                                if (item) {
                                  useCartStore.getState().updateQty(item.id, item.qty + 1);
                                }
                              }}
                              className="w-10 h-10 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 text-white flex items-center justify-center hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300"
                            >
                              <Plus className="w-4 h-4" />
                            </motion.button>
                          </motion.div>
                        ) : (
                          <motion.button
                            whileHover={{ scale: 1.05, y: -2 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => addToCart(product)}
                            className="px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-xl font-semibold hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 border border-white/20"
                          >
                            <div className="flex items-center gap-2">
                              <ShoppingCart className="w-4 h-4" />
                              Add to Cart
                            </div>
                          </motion.button>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}

            {/* Grid Layout */}
            {layoutMode === 'grid' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredProducts.map((product, index) => (
                  <motion.div
                    key={product.id}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1 + index * 0.1 }}
                    whileHover={{ scale: 1.05, y: -10 }}
                    className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 shadow-xl hover:shadow-2xl transition-all duration-500 group"
                  >
                    {/* Product Image */}
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      className="relative cursor-pointer mb-4"
                      onClick={() => handleProductClick(product)}
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <EnhancedImage
                        src={product.image}
                        alt={product.name}
                        className="relative w-full h-40 object-cover rounded-2xl border-2 border-white/30 shadow-lg"
                        fallbackType="product"
                        name={product.name}
                        category={product.category}
                      />
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center"
                      >
                        <Sparkles className="w-3 h-3 text-white" />
                      </motion.div>
                    </motion.div>

                    {/* Product Info */}
                    <div className="space-y-3">
                      <motion.h4
                        whileHover={{ x: 5 }}
                        className="font-bold text-lg text-white cursor-pointer hover:text-purple-300 transition-colors duration-300"
                        onClick={() => handleProductClick(product)}
                      >
                        {product.name}
                      </motion.h4>
                      {product.description && (
                        <p className="text-white/70 text-sm leading-relaxed line-clamp-2">{product.description}</p>
                      )}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {product.discountPrice && product.discountPrice < product.price && (
                            <motion.p
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ delay: 1.2 + index * 0.1 }}
                              className="text-sm font-semibold text-white/50 line-through"
                            >
                              ₪{product.price}
                            </motion.p>
                          )}
                          <motion.p
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 1.2 + index * 0.1 }}
                            className="text-xl font-bold bg-gradient-to-r from-green-400 to-emerald-500 bg-clip-text text-transparent"
                          >
                            ₪{product.discountPrice || product.price}
                          </motion.p>
                        </div>
                        {product.discountPrice && product.discountPrice < product.price ? (
                          <span className="px-2 py-1 bg-red-500/20 text-red-300 text-xs rounded-full border border-red-500/30">
                            Sale!
                          </span>
                        ) : (
                          <span className="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full border border-green-500/30">
                            Best Price
                          </span>
                        )}
                      </div>

                      {/* Cart Controls */}
                      <div className="pt-2">
                        {getCartQty(product.id) > 0 ? (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="flex items-center justify-center gap-3 bg-white/10 backdrop-blur-sm rounded-xl p-2 border border-white/20"
                          >
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => {
                                const item = items.find(i => i.product.id === product.id && i.supplierId === supplier.id);
                                if (item && item.qty > 1) {
                                  useCartStore.getState().updateQty(item.id, item.qty - 1);
                                } else if (item) {
                                  useCartStore.getState().removeItem(item.id);
                                }
                              }}
                              className="w-8 h-8 rounded-lg bg-gradient-to-r from-red-500 to-pink-500 text-white flex items-center justify-center hover:shadow-lg hover:shadow-red-500/25 transition-all duration-300"
                            >
                              <Minus className="w-3 h-3" />
                            </motion.button>
                            <motion.span
                              key={`cart-qty-grid-${product.id}`}
                              initial={{ scale: 1.5 }}
                              animate={{ scale: 1 }}
                              className="w-8 text-center font-bold text-white"
                            >
                              {getCartQty(product.id)}
                            </motion.span>
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => {
                                const item = items.find(i => i.product.id === product.id && i.supplierId === supplier.id);
                                if (item) {
                                  useCartStore.getState().updateQty(item.id, item.qty + 1);
                                }
                              }}
                              className="w-8 h-8 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 text-white flex items-center justify-center hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300"
                            >
                              <Plus className="w-3 h-3" />
                            </motion.button>
                          </motion.div>
                        ) : (
                          <motion.button
                            whileHover={{ scale: 1.05, y: -2 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => addToCart(product)}
                            className="w-full py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-xl font-semibold hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 border border-white/20"
                          >
                            <div className="flex items-center justify-center gap-2">
                              <ShoppingCart className="w-4 h-4" />
                              Add to Cart
                            </div>
                          </motion.button>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}

            {/* Compact Layout */}
            {layoutMode === 'compact' && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {filteredProducts.map((product, index) => (
                  <motion.div
                    key={product.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1 + index * 0.05 }}
                    whileHover={{ scale: 1.05, y: -5 }}
                    className="bg-white/10 backdrop-blur-xl rounded-xl border border-white/20 p-4 shadow-lg hover:shadow-xl transition-all duration-300 group"
                  >
                    {/* Compact Product Image */}
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      className="relative cursor-pointer mb-3"
                      onClick={() => handleProductClick(product)}
                    >
                      <EnhancedImage
                        src={product.image}
                        alt={product.name}
                        className="w-full h-24 object-cover rounded-lg border border-white/30"
                        fallbackType="product"
                        name={product.name}
                        category={product.category}
                      />
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center"
                      >
                        <Sparkles className="w-2 h-2 text-white" />
                      </motion.div>
                    </motion.div>

                    {/* Compact Product Info */}
                    <div className="space-y-2">
                      <motion.h4
                        className="font-semibold text-sm text-white cursor-pointer hover:text-purple-300 transition-colors duration-300 line-clamp-1"
                        onClick={() => handleProductClick(product)}
                      >
                        {product.name}
                      </motion.h4>
                      <div className="flex items-center gap-1">
                        {product.discountPrice && product.discountPrice < product.price && (
                          <motion.p
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 1.1 + index * 0.05 }}
                            className="text-xs font-semibold text-white/50 line-through"
                          >
                            ₪{product.price}
                          </motion.p>
                        )}
                        <motion.p
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: 1.1 + index * 0.05 }}
                          className="text-lg font-bold bg-gradient-to-r from-green-400 to-emerald-500 bg-clip-text text-transparent"
                        >
                          ₪{product.discountPrice || product.price}
                        </motion.p>
                      </div>

                      {/* Compact Cart Controls */}
                      <div className="pt-1">
                        {getCartQty(product.id) > 0 ? (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="flex items-center justify-center gap-2 bg-white/10 backdrop-blur-sm rounded-lg p-1 border border-white/20"
                          >
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => {
                                const item = items.find(i => i.product.id === product.id && i.supplierId === supplier.id);
                                if (item && item.qty > 1) {
                                  useCartStore.getState().updateQty(item.id, item.qty - 1);
                                } else if (item) {
                                  useCartStore.getState().removeItem(item.id);
                                }
                              }}
                              className="w-6 h-6 rounded bg-gradient-to-r from-red-500 to-pink-500 text-white flex items-center justify-center hover:shadow-lg transition-all duration-300"
                            >
                              <Minus className="w-2 h-2" />
                            </motion.button>
                            <motion.span
                              key={`cart-qty-compact-${product.id}`}
                              initial={{ scale: 1.5 }}
                              animate={{ scale: 1 }}
                              className="w-6 text-center font-bold text-white text-sm"
                            >
                              {getCartQty(product.id)}
                            </motion.span>
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => {
                                const item = items.find(i => i.product.id === product.id && i.supplierId === supplier.id);
                                if (item) {
                                  useCartStore.getState().updateQty(item.id, item.qty + 1);
                                }
                              }}
                              className="w-6 h-6 rounded bg-gradient-to-r from-green-500 to-emerald-500 text-white flex items-center justify-center hover:shadow-lg transition-all duration-300"
                            >
                              <Plus className="w-2 h-2" />
                            </motion.button>
                          </motion.div>
                        ) : (
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => addToCart(product)}
                            className="w-full py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg text-xs font-semibold hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 border border-white/20"
                          >
                            <div className="flex items-center justify-center gap-1">
                              <ShoppingCart className="w-3 h-3" />
                              Add
                            </div>
                          </motion.button>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </motion.div>

          {/* Premium Empty State */}
          {filteredProducts.length === 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
              className="text-center py-16"
            >
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="text-8xl mb-6"
              >
                🔍
              </motion.div>
              <motion.h3
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-2xl font-bold text-white mb-3"
              >
                No products found
              </motion.h3>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-white/70 text-lg"
              >
                Try adjusting your search terms or category
              </motion.p>
            </motion.div>
          )}
        </div>


      </div>
    </>
  );
};

export default SupplierDetailsPage;